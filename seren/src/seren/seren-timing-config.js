export class SerenTimingConfig {
  static #timingProfile = {
    // Message Processing Timings (A-0 to A-7)
    messageProcessing: {
      // A-0: Check if message already sent in DB
      duplicateCheck: { min: 0, max: 0, default: 0 },
      
      // A-1: Scroll to top and wait (2-4 seconds)
      scrollToTop: { min: 2000, max: 4000, default: 3000 },
      
      // A-2: Sidebar hops (300ms each, 2-3 seconds total)
      sidebarScrollHop: { min: 300, max: 300, default: 300 },
      maxSidebarScrollTime: { min: 2000, max: 3000, default: 2500 },
      
      // A-3: Click room link + wait (1-3 seconds)
      roomNavigation: { min: 1000, max: 3000, default: 2000 },
      afterRoomClickDelay: { min: 500, max: 500, default: 500 },
      
      // A-4: Extra wait from execActions (1-3 seconds)
      execActionsDelay: { min: 1000, max: 3000, default: 2000 },
      
      // A-5a: Text message internal wait (1-3 seconds)
      textMessage: { min: 1000, max: 3000, default: 2000 },
      textMessageExtraWait: { min: 0, max: 0, default: 0 },
      
      // A-5b: File upload (1.0-2.0 seconds)
      fileUpload: { min: 1000, max: 2000, default: 1500 },
      
      // A-6: Poll DOM every 300ms (≤4s total, 1-3 seconds expected)
      domPollInterval: { min: 300, max: 300, default: 300 },
      domPollMaxTime: 4000,
      
      // A-7: Mark processed in DB (200ms)
      markProcessed: { min: 200, max: 200, default: 200 }
    },
    
    // Invite handling timings (Process B)
    invites: {
      // B-0: Wait before accept (1-3s)
      waitBeforeAccept: { min: 1000, max: 3000, default: 2000 },
      
      // B-1: Go to requests page (1-3s)
      navigateToRequests: { min: 1000, max: 3000, default: 2000 },
      
      // B-2: Internal wait after navigation (1-3s)
      afterNavigationWait: { min: 1000, max: 3000, default: 2000 },
      
      // B-3: Click request room (2-4s)
      clickRequestRoom: { min: 2000, max: 4000, default: 3000 },
      
      // B-4: Internal wait after click (2-4s)
      afterClickWait: { min: 2000, max: 4000, default: 3000 },
      
      // B-5: Accept/Ignore request (300-600ms)
      acceptOrIgnore: { min: 300, max: 600, default: 450 },
      
      // B-6: Send welcome message (0.5-3s if enabled)
      sendWelcomeMessage: { min: 500, max: 3000, default: 1750 },
      
      // B-7: Save accepted invite (15-30ms)
      saveInvite: { min: 15, max: 30, default: 20 },
      
      // B-8: Return to main chat + wait (1-3s total)
      returnToMainChat: { min: 1000, max: 3000, default: 2000 },
      afterReturnWait: { min: 0, max: 0, default: 0 }, // Included in returnToMainChat
      
      // B-9: Wait after accept (3-6s)
      waitAfterAccept: { min: 3000, max: 6000, default: 4500 },
      
      // Legacy/alternative timings (kept for compatibility)
      processInvite: { min: 5000, max: 10000, default: 7500 }
    },
    
    // Background operation timings
    background: {
      storageRead: { min: 20, max: 50, default: 30 },
      storageWrite: { min: 15, max: 30, default: 20 },
      networkRequest: { min: 200, max: 500, default: 350 }
    },
    
    // System timings
    system: {
      cycleInterval: 30000,
      maxCycleDuration: 60000,
      retryDelay: 5000,
      dbQuery: { min: 30, max: 60, default: 40 }
    }
  };

  static #operationSequences = {
    // Message processing sequence (A-0 to A-7)
    processMessage: [
      // A-0: Duplicate check in DB
      { name: 'duplicateCheckDb', dependsOn: null },
      
      // A-1: Scroll to top and wait
      { name: 'scrollToTop', dependsOn: 'duplicateCheckDb' },
      
      // A-2: Sidebar scrolling
      { name: 'sidebarScrollHop', dependsOn: 'scrollToTop' },
      { name: 'maxSidebarScroll', dependsOn: 'sidebarScrollHop' },
      
      // A-3: Room navigation
      { name: 'roomNavigation', dependsOn: 'maxSidebarScroll' },
      { name: 'afterRoomClick', dependsOn: 'roomNavigation' },
      
      // A-4: Extra wait in execActions
      { name: 'execActionsWait', dependsOn: 'afterRoomClick' },
      
      // A-5a: Text message sending (alternative to A-5b)
      { name: 'sendTextMessage', dependsOn: 'execActionsWait' },
      { name: 'textMessageExtraWait', dependsOn: 'sendTextMessage' },
      
      // A-5b: File upload (alternative to A-5a)
      { name: 'fileUpload', dependsOn: 'execActionsWait' },
      
      // A-6: DOM polling
      { name: 'domPolling', dependsOn: 'textMessageExtraWait' },
      
      // A-7: Mark processed in DB
      { name: 'markProcessed', dependsOn: 'domPolling' }
    ],
    
    // Legacy message sending (simplified)
    sendMessage: [
      { name: 'validateInput', dependsOn: null },
      { name: 'checkDuplicates', dependsOn: 'validateInput' },
      { name: 'focusInput', dependsOn: 'checkDuplicates' },
      { name: 'typeMessage', dependsOn: 'focusInput' },
      { name: 'clickSend', dependsOn: 'typeMessage' },
      { name: 'verifySent', dependsOn: 'clickSend' }
    ],
    
    // Invite acceptance flow (Process B)
    acceptInvite: [
      // B-0: Initial wait before accepting
      { name: 'waitBeforeAccept', dependsOn: null },
      
      // B-1: Navigate to requests page
      { name: 'navigateToRequests', dependsOn: 'waitBeforeAccept' },
      
      // B-2: Wait after navigation
      { name: 'afterNavigationWait', dependsOn: 'navigateToRequests' },
      
      // B-3: Find and click request room
      { name: 'findInvite', dependsOn: 'afterNavigationWait' },
      { name: 'clickRequestRoom', dependsOn: 'findInvite' },
      
      // B-4: Wait after clicking
      { name: 'afterClickWait', dependsOn: 'clickRequestRoom' },
      
      // B-5: Accept/Ignore request
      { name: 'acceptOrIgnore', dependsOn: 'afterClickWait' },
      
      // B-6: Optional welcome message
      { name: 'sendWelcomeMessage', dependsOn: 'acceptOrIgnore', optional: true },
      
      // B-7: Save the accepted invite
      { name: 'saveInvite', dependsOn: 'acceptOrIgnore' },
      
      // B-8: Return to main chat
      { name: 'returnToMainChat', dependsOn: 'saveInvite' },
      { name: 'afterReturnWait', dependsOn: 'returnToMainChat' },
      
      // B-9: Final wait after accepting
      { name: 'waitAfterAccept', dependsOn: 'afterReturnWait' }
    ],
    
    // Main cycle processing
    waveCycle: [
      { name: 'acquireLock', dependsOn: null },
      { name: 'processObservations', dependsOn: 'acquireLock' },
      { name: 'handleInvites', dependsOn: 'processObservations' },
      { name: 'processInstructions', dependsOn: 'handleInvites' },
      { name: 'updateStatus', dependsOn: 'processInstructions' },
      { name: 'releaseLock', dependsOn: 'updateStatus' }
    ]
  };

  static #stepTimings = {
    // Message Processing Steps (A-0 to A-7)
    // A-0
    duplicateCheckDb: this.#timingProfile.messageProcessing.duplicateCheck,
    
    // A-1
    scrollToTop: this.#timingProfile.messageProcessing.scrollToTop,
    
    // A-2
    sidebarScrollHop: this.#timingProfile.messageProcessing.sidebarScrollHop,
    maxSidebarScroll: this.#timingProfile.messageProcessing.maxSidebarScrollTime,
    
    // A-3
    roomNavigation: this.#timingProfile.messageProcessing.roomNavigation,
    afterRoomClick: this.#timingProfile.messageProcessing.afterRoomClickDelay,
    
    // A-4
    execActionsWait: this.#timingProfile.messageProcessing.execActionsDelay,
    
    // A-5a
    sendTextMessage: this.#timingProfile.messageProcessing.textMessage,
    textMessageExtraWait: this.#timingProfile.messageProcessing.textMessageExtraWait,
    
    // A-5b
    fileUpload: this.#timingProfile.messageProcessing.fileUpload,
    
    // A-6
    domPolling: {
      interval: this.#timingProfile.messageProcessing.domPollInterval,
      maxTime: this.#timingProfile.messageProcessing.domPollMaxTime
    },
    
    // A-7
    markProcessed: this.#timingProfile.messageProcessing.markProcessed,
    
    // Navigation Steps
    navigateToRequests: this.#timingProfile.invites.navigateToRequests,
    
    // Invite Handling Steps (Process B)
    // B-0: Initial wait before accepting
    waitBeforeAccept: this.#timingProfile.invites.waitBeforeAccept,
    
    // B-1: Navigate to requests page
    navigateToRequestsInvite: this.#timingProfile.invites.navigateToRequests,
    
    // B-2: Wait after navigation
    afterNavigationWait: this.#timingProfile.invites.afterNavigationWait,
    
    // B-3: Click on request room
    clickRequestRoom: this.#timingProfile.invites.clickRequestRoom,
    
    // B-4: Wait after clicking request
    afterClickWait: this.#timingProfile.invites.afterClickWait,
    
    // B-5: Accept/Ignore request
    acceptOrIgnore: this.#timingProfile.invites.acceptOrIgnore,
    
    // B-6: Send welcome message (if enabled)
    sendWelcomeMessage: this.#timingProfile.invites.sendWelcomeMessage,
    
    // B-7: Save accepted invite
    saveInvite: this.#timingProfile.invites.saveInvite,
    
    // B-8: Return to main chat and wait
    returnToMainChat: this.#timingProfile.invites.returnToMainChat,
    afterReturnWait: this.#timingProfile.invites.afterReturnWait,
    
    // B-9: Final wait after accepting
    waitAfterAccept: this.#timingProfile.invites.waitAfterAccept,
   
  };

  static getTimingProfile() {
    return JSON.parse(JSON.stringify(this.#timingProfile));
  }

  static getOperationSequence(operationType) {
    return JSON.parse(JSON.stringify(this.#operationSequences[operationType] || []));
  }

  static getStepTiming(stepName) {
    const timing = this.#stepTimings[stepName];
    return timing ? { ...timing } : { min: 0, max: 0, default: 0 };
  }

  static getRandomDelay(stepName, useMinMax = false) {
    const timing = this.getStepTiming(stepName);
    if (useMinMax) {
      return Math.floor(Math.random() * (timing.max - timing.min + 1)) + timing.min;
    }
    return timing.default;
  }

  static async waitForStep(stepName) {
    const delay = this.getRandomDelay(stepName, true);
    await new Promise(resolve => setTimeout(resolve, delay));
    return delay;
  }
}
