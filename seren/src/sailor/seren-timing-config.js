if (!window.SerenTimingConfig) {
  class SerenTimingConfig {
    static #timingProfile = {
      messageProcessing: {
        duplicateCheck: { min: 0, max: 0, default: 0 },
        scrollToTop: { min: 2000, max: 4000, default: 3000 },
        sidebarScrollHop: { min: 300, max: 300, default: 300 },
        maxSidebarScrollTime: { min: 2000, max: 3000, default: 2500 },
        roomNavigation: { min: 1000, max: 3000, default: 2000 },
        afterRoomClickDelay: { min: 500, max: 500, default: 500 },
        execActionsDelay: { min: 1000, max: 3000, default: 2000 },
        textMessage: { min: 1000, max: 3000, default: 2000 },
        textMessageExtraWait: { min: 0, max: 0, default: 0 },
        fileUpload: { min: 1000, max: 2000, default: 1500 },
        domPollInterval: { min: 300, max: 300, default: 300 },
        domPollMaxTime: 4000,
        markProcessed: { min: 200, max: 200, default: 200 }
      },
      invites: {
        waitBeforeAccept: { min: 1000, max: 3000, default: 2000 },
        navigateToRequests: { min: 1000, max: 3000, default: 2000 },
        afterNavigationWait: { min: 1000, max: 3000, default: 2000 },
        clickRequestRoom: { min: 2000, max: 4000, default: 3000 },
        afterClickWait: { min: 2000, max: 4000, default: 3000 },
        acceptOrIgnore: { min: 300, max: 600, default: 450 },
        sendWelcomeMessage: { min: 500, max: 3000, default: 1750 },
        saveInvite: { min: 15, max: 30, default: 20 },
        returnToMainChat: { min: 1000, max: 3000, default: 2000 },
        afterReturnWait: { min: 0, max: 0, default: 0 },
        waitAfterAccept: { min: 3000, max: 6000, default: 4500 },
        processInvite: { min: 5000, max: 10000, default: 7500 }
      },
      background: {
        storageRead: { min: 20, max: 50, default: 30 },
        storageWrite: { min: 15, max: 30, default: 20 },
        networkRequest: { min: 200, max: 500, default: 350 }
      },
      system: {
        cycleInterval: 30000,
        maxCycleDuration: 60000,
        retryDelay: 5000,
        dbQuery: { min: 30, max: 60, default: 40 }
      }
    };

    static #stepTimings = {
      duplicateCheckDb: this.#timingProfile.messageProcessing.duplicateCheck,
      scrollToTop: this.#timingProfile.messageProcessing.scrollToTop,
      sidebarScrollHop: this.#timingProfile.messageProcessing.sidebarScrollHop,
      maxSidebarScroll: this.#timingProfile.messageProcessing.maxSidebarScrollTime,
      roomNavigation: this.#timingProfile.messageProcessing.roomNavigation,
      afterRoomClick: this.#timingProfile.messageProcessing.afterRoomClickDelay,
      execActionsWait: this.#timingProfile.messageProcessing.execActionsDelay,
      sendTextMessage: this.#timingProfile.messageProcessing.textMessage,
      textMessageExtraWait: this.#timingProfile.messageProcessing.textMessageExtraWait,
      fileUpload: this.#timingProfile.messageProcessing.fileUpload,
      domPolling: {
        interval: this.#timingProfile.messageProcessing.domPollInterval,
        maxTime: this.#timingProfile.messageProcessing.domPollMaxTime
      },
      markProcessed: this.#timingProfile.messageProcessing.markProcessed,
      navigateToRequests: this.#timingProfile.invites.navigateToRequests,
      waitBeforeAccept: this.#timingProfile.invites.waitBeforeAccept,
      navigateToRequestsInvite: this.#timingProfile.invites.navigateToRequests,
      afterNavigationWait: this.#timingProfile.invites.afterNavigationWait,
      clickRequestRoom: this.#timingProfile.invites.clickRequestRoom,
      afterClickWait: this.#timingProfile.invites.afterClickWait,
      acceptOrIgnore: this.#timingProfile.invites.acceptOrIgnore,
      sendWelcomeMessage: this.#timingProfile.invites.sendWelcomeMessage,
      saveInvite: this.#timingProfile.invites.saveInvite,
      returnToMainChat: this.#timingProfile.invites.returnToMainChat,
      afterReturnWait: this.#timingProfile.invites.afterReturnWait,
      waitAfterAccept: this.#timingProfile.invites.waitAfterAccept
    };

    static getTimingProfile() {
      return JSON.parse(JSON.stringify(this.#timingProfile));
    }

    static getStepTiming(stepName) {
      const timing = this.#stepTimings[stepName];
      return timing ? { ...timing } : { min: 0, max: 0, default: 0 };
    }

    static getRandomDelay(stepName, useMinMax = false) {
      const timing = this.getStepTiming(stepName);
      if (useMinMax) {
        return Math.floor(Math.random() * (timing.max - timing.min + 1)) + timing.min;
      }
      return timing.default;
    }

    static async waitForStep(stepName) {
      const delay = this.getRandomDelay(stepName, true);
      await new Promise(resolve => setTimeout(resolve, delay));
      return delay;
    }
  }

  window.SerenTimingConfig = SerenTimingConfig;
}
