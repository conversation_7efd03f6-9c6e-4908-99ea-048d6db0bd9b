import { logger } from '../shared/logger-client.js'


class ScriptInjector {
  constructor() {
    this.loadedScripts = {}
    this.loadPromises = {}
    this.SCRIPT_FILES = ['/sailor/storage.js', '/sailor/page-clock.js','/sailor/rc-page.js','/sailor/rc-rooms.js','/sailor/abilities.js','/sailor/seren-timing-config.js','/sailor/actions.js', '/sailor/requests-actions.js']
  }

  isScriptLoaded = (tabId) => !!this.loadedScripts[tabId]

  loadScripts = async (tabId) => {
    if (!tabId) {
      logger('ScriptInjector', 'No tabId provided', 'error');
      return { status: 'error', message: 'No tabId provided' };
    }

    try {
      // Check if tab exists and is accessible
      const tab = await chrome.tabs.get(tabId).catch(() => null);
      if (!tab) {
        logger('ScriptInjector', `Tab ${tabId} not found`, 'error');
        return { status: 'error', message: 'Tab not found' };
      }

      // Check if scripts are already loaded for this tab
      if (this.loadedScripts[tabId]) {
        return { status: 'already-loaded', tab };
      }

      // Check if we're already loading scripts for this tab
      if (this.loadPromises[tabId]) {
        return this.loadPromises[tabId];
      }

      this.loadPromises[tabId] = (async () => {
        try {
          // Wait for the tab to be fully loaded
          if (tab.status !== 'complete') {
            await new Promise((resolve) => {
              const onUpdated = (updatedTabId, changeInfo) => {
                if (updatedTabId === tabId && changeInfo.status === 'complete') {
                  chrome.tabs.onUpdated.removeListener(onUpdated);
                  resolve();
                }
              };
              chrome.tabs.onUpdated.addListener(onUpdated);
              // Add a timeout just in case
              setTimeout(() => {
                chrome.tabs.onUpdated.removeListener(onUpdated);
                resolve();
              }, 10000);
            });
          }

          // Inject the scripts
          const results = await chrome.scripting.executeScript({
            target: { tabId },
            files: this.SCRIPT_FILES,
          });
          
          this.loadedScripts[tabId] = true;
          return { status: 'loaded', results, tab };
        } catch (error) {
          logger('ScriptInjector', `Error loading scripts: ${error.message}`, 'error');
          throw error;
        } finally {
          delete this.loadPromises[tabId];
        }
      })();

      return this.loadPromises[tabId];
    } catch (error) {
      logger('ScriptInjector', `Failed to load scripts: ${error.message}`, 'error');
      return { status: 'error', message: error.message };
    }
  }

  resetLoadStatus = (tabId) => {
    delete this.loadedScripts[tabId]
    delete this.loadPromises[tabId]
  }

  setScriptLoaded = (tabId, status) => {
    this.loadedScripts[tabId] = status
  }

  handleTabUpdated = (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('reddit.com')) {
      this.resetLoadStatus(tabId)
      this.loadScripts(tabId).catch(err => {
        logger('ScriptInjector', `Error on auto-loading scripts for tab ${tabId}: ${err}`, 'error')
      })
    }
  }

  handleTabRemoved = (tabId) => {
    this.resetLoadStatus(tabId)
  }

  setupListeners = () => async () => {
    await chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdated(tabId, changeInfo, tab)
    })
    await chrome.tabs.onRemoved.addListener((tabId) => {
      this.handleTabRemoved(tabId)
    })
  }
}

export const scriptInjector = new ScriptInjector()